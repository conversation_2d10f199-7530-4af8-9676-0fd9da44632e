<template>
  <a-modal
    v-model:open="visible"
    title="配置SSH远程开发密钥"
    :width="480"
    @cancel="handleCancel"
    @ok="handleConfirm"
  >
    <div class="create-key-content">
      <p>当前项目空间尚未配置SSH远程开发密钥，您可立即创建密钥</p>
      <p>
        配置完成后，项目成员（负责人、管理员、开发人员）可使用该密钥，在本地通过Xshell、Pycharm、VSCode等工具，连接模型训练（开发环境、训练任务）实例，实现SSH远程开发。
      </p>
    </div>
    
    <template #footer>
      <a-space>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleConfirm">创建密钥</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'confirm']);

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const handleCancel = () => {
  visible.value = false;
};

const handleConfirm = () => {
  emit('confirm');
  visible.value = false;
};
</script>

<style lang="less" scoped>
.create-key-content {
  padding: 16px 0;
  
  p {
    margin-bottom: 16px;
    line-height: 1.6;
    color: rgba(0, 20, 26, 0.85);
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>

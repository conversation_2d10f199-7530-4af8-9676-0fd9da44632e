<template>
  <a-modal
    v-model:open="visible"
    title="SSH远程开发使用方式"
    :width="600"
    :footer="null"
    @cancel="handleCancel"
  >
    <a-spin :spinning="loading">
      <div class="ssh-modal-content">
        <!-- 使用说明文案 -->
        <div class="ssh-description">
          {{ moduleText }}，具体操作方式详见
          <a v-if="helpCenterUrl" :href="helpCenterUrl" target="_blank" rel="noopener noreferrer">帮助中心</a>
          <span v-else>帮助中心</span>
        </div>

        <!-- SSH密钥信息 -->
        <div v-if="sshKeyInfo && sshKeyInfo.hasKey" class="ssh-key-info">
          <div class="ssh-key-item">
            <span class="ssh-key-label">SSH密钥指纹：</span>
            <span class="ssh-key-value">{{ sshKeyInfo.fingerprint }}</span>
          </div>
          <div class="ssh-key-item">
            <span class="ssh-key-label">更新时间：</span>
            <span class="ssh-key-value">{{ sshKeyInfo.updateTime }}</span>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="ssh-actions">
          <!-- 未创建密钥时 -->
          <template v-if="!sshKeyInfo || !sshKeyInfo.hasKey">
            <div v-if="!canCreate" class="no-key-message">
              当前项目空间尚未配置SSH远程开发密钥，您可立即创建密钥
            </div>
            <a-button
              v-if="canCreate"
              type="primary"
              @click="handleCreateKey"
            >
              创建密钥
            </a-button>
          </template>

          <!-- 已创建密钥时 -->
          <template v-else>
            <a-space>
              <a-button
                v-if="canDownload"
                type="primary"
                @click="handleDownloadKey"
              >
                下载私钥
              </a-button>
              <a-button
                v-if="canRecreate"
                @click="handleRecreateKey"
              >
                重新创建密钥
              </a-button>
            </a-space>
          </template>
        </div>
      </div>
    </a-spin>

    <!-- 创建密钥确认弹窗 -->
    <SSHCreateKeyModal
      v-model:visible="showCreateModal"
      @confirm="confirmCreateKey"
    />

    <!-- 重新创建密钥确认弹窗 -->
    <a-modal
      v-model:open="showRecreateModal"
      title="重新创建密钥"
      :width="480"
      @cancel="showRecreateModal = false"
      @ok="confirmRecreateKey"
    >
      <p>重新创建密钥将会覆盖当前密钥，确定要继续吗？</p>
    </a-modal>

    <!-- 验证码弹窗 -->
    <SSHVerificationModal
      v-model:visible="showVerificationModal"
      @verify="handleVerifyAndDownload"
    />
  </a-modal>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { useStore } from 'vuex';
import { ROLE_KEYS } from '@/constants/projectSpace';
import { SSH_MESSAGES, SSH_HELP_CENTER_URL } from '@/constants/ssh';
import {
  checkSSHPermission,
  getModuleText,
  getSSHKeyInfo,
  createSSHKey,
  recreateSSHKey,
  downloadPrivateKey,
  getModuleVisibility,
  triggerDownload
} from '../sshUtils';
import { getCurrentRole } from '../utils';
import SSHCreateKeyModal from './SSHCreateKeyModal.vue';
import SSHVerificationModal from './SSHVerificationModal.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible']);

const store = useStore();
const loading = ref(false);
const sshKeyInfo = ref(null);
const userRole = ref('');
const moduleVisibility = ref({});
const showCreateModal = ref(false);
const showRecreateModal = ref(false);
const showVerificationModal = ref(false);

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const canManage = computed(() => checkSSHPermission(userRole.value, 'canManage'));
const canCreate = computed(() => checkSSHPermission(userRole.value, 'canCreate'));
const canRecreate = computed(() => checkSSHPermission(userRole.value, 'canRecreate'));
const canDownload = computed(() => checkSSHPermission(userRole.value, 'canDownload'));

const moduleText = computed(() => {
  return getModuleText(
    moduleVisibility.value.devEnvVisible,
    moduleVisibility.value.trainTaskVisible
  );
});

const helpCenterUrl = computed(() => SSH_HELP_CENTER_URL);

// 方法
const handleCancel = () => {
  visible.value = false;
};

const loadSSHKeyInfo = async () => {
  try {
    loading.value = true;
    const res = await getSSHKeyInfo();
    if (res.code === 0) {
      sshKeyInfo.value = res.data;
    }
  } catch (error) {
    console.error('加载SSH密钥信息失败:', error);
  } finally {
    loading.value = false;
  }
};

const loadModuleVisibility = async () => {
  try {
    const res = await getModuleVisibility();
    if (res.code === 0) {
      moduleVisibility.value = res.data;
    }
  } catch (error) {
    console.error('加载模块显隐状态失败:', error);
  }
};

const loadUserRole = async () => {
  try {
    const { role } = await getCurrentRole();
    userRole.value = role;
  } catch (error) {
    console.error('获取用户角色失败:', error);
  }
};

const handleCreateKey = () => {
  showCreateModal.value = true;
};

const confirmCreateKey = async () => {
  try {
    loading.value = true;
    const res = await createSSHKey();
    if (res.code === 0) {
      message.success(SSH_MESSAGES.CREATE_SUCCESS);
      // 自动下载私钥文件
      if (res.data && res.data.privateKey) {
        const blob = new Blob([res.data.privateKey], { type: 'text/plain' });
        triggerDownload(blob, 'ssh_private_key');
      }
      // 刷新密钥信息
      await loadSSHKeyInfo();
    } else {
      message.error(SSH_MESSAGES.CREATE_FAILED);
    }
  } catch (error) {
    message.error(SSH_MESSAGES.CREATE_FAILED);
  } finally {
    loading.value = false;
  }
};

const handleRecreateKey = () => {
  showRecreateModal.value = true;
};

const confirmRecreateKey = async () => {
  try {
    loading.value = true;
    showRecreateModal.value = false;
    const res = await recreateSSHKey();
    if (res.code === 0) {
      message.success(SSH_MESSAGES.RECREATE_SUCCESS);
      // 自动下载私钥文件
      if (res.data && res.data.privateKey) {
        const blob = new Blob([res.data.privateKey], { type: 'text/plain' });
        triggerDownload(blob, 'ssh_private_key');
      }
      // 刷新密钥信息
      await loadSSHKeyInfo();
    } else {
      message.error(SSH_MESSAGES.RECREATE_FAILED);
    }
  } catch (error) {
    message.error(SSH_MESSAGES.RECREATE_FAILED);
  } finally {
    loading.value = false;
  }
};

const handleDownloadKey = () => {
  showVerificationModal.value = true;
};

const handleVerifyAndDownload = async (verificationCode) => {
  try {
    loading.value = true;
    const res = await downloadPrivateKey(verificationCode);
    if (res.code === 0) {
      message.success(SSH_MESSAGES.DOWNLOAD_SUCCESS);
      // 下载私钥文件
      if (res.data && res.data.privateKey) {
        const blob = new Blob([res.data.privateKey], { type: 'text/plain' });
        triggerDownload(blob, 'ssh_private_key');
      }
      showVerificationModal.value = false;
    } else {
      message.error(res.message || SSH_MESSAGES.DOWNLOAD_FAILED);
    }
  } catch (error) {
    message.error(SSH_MESSAGES.DOWNLOAD_FAILED);
  } finally {
    loading.value = false;
  }
};

// 监听弹窗显示状态
watch(visible, (newVal) => {
  if (newVal) {
    loadUserRole();
    loadModuleVisibility();
    loadSSHKeyInfo();
  }
});

onMounted(() => {
  if (visible.value) {
    loadUserRole();
    loadModuleVisibility();
    loadSSHKeyInfo();
  }
});
</script>

<style lang="less" scoped>
.ssh-modal-content {
  .ssh-description {
    margin-bottom: 24px;
    line-height: 1.6;
    color: rgba(0, 20, 26, 0.85);
    
    a {
      color: #00a2f4;
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }

  .ssh-key-info {
    margin-bottom: 24px;
    padding: 16px;
    background: rgba(0, 20, 26, 0.02);
    border-radius: 4px;

    .ssh-key-item {
      display: flex;
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }

      .ssh-key-label {
        min-width: 100px;
        color: rgba(0, 20, 26, 0.7);
      }

      .ssh-key-value {
        color: #00141a;
        word-break: break-all;
      }
    }
  }

  .ssh-actions {
    text-align: center;

    .no-key-message {
      margin-bottom: 16px;
      color: rgba(0, 20, 26, 0.7);
    }
  }
}
</style>

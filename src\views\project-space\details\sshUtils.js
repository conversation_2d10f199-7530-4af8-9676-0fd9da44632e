import { sshApi } from '@/apis';
import { ROLE_KEYS } from '@/constants/projectSpace';
import { SSH_ROLE_PERMISSIONS, MODULE_TEXT_MAP } from '@/constants/ssh';
import { getCurrentRole } from './utils';

/**
 * 检查用户是否有SSH功能权限
 * @param {string} role - 用户角色
 * @param {string} permission - 权限类型 (canView, canManage, canCreate, etc.)
 * @returns {boolean}
 */
export const checkSSHPermission = (role, permission) => {
  const rolePermissions = SSH_ROLE_PERMISSIONS[role];
  return rolePermissions ? rolePermissions[permission] : false;
};

/**
 * 检查是否显示SSH入口
 * @param {boolean} poolSSHEnabled - 资源池SSH远程开发是否开启
 * @param {boolean} projectDataExportEnabled - 项目空间数据出口是否开启
 * @param {string} userRole - 用户角色
 * @returns {boolean}
 */
export const shouldShowSSHEntry = (poolSSHEnabled, projectDataExportEnabled, userRole) => {
  // 资源池关闭SSH或项目空间关闭数据出口时，不显示
  if (!poolSSHEnabled || !projectDataExportEnabled) {
    return false;
  }
  
  // 标注人员不显示
  if (userRole === ROLE_KEYS.LABELER) {
    return false;
  }
  
  // 其他角色可以查看
  return checkSSHPermission(userRole, 'canView');
};

/**
 * 获取模块显示文案
 * @param {boolean} devEnvVisible - 开发环境模块是否可见
 * @param {boolean} trainTaskVisible - 训练任务模块是否可见
 * @returns {string}
 */
export const getModuleText = (devEnvVisible, trainTaskVisible) => {
  if (devEnvVisible && trainTaskVisible) {
    return MODULE_TEXT_MAP.BOTH_VISIBLE;
  } else if (devEnvVisible && !trainTaskVisible) {
    return MODULE_TEXT_MAP.ONLY_DEV_ENV;
  } else if (!devEnvVisible && trainTaskVisible) {
    return MODULE_TEXT_MAP.ONLY_TRAIN_TASK;
  }
  return MODULE_TEXT_MAP.BOTH_VISIBLE; // 默认显示两个模块
};

/**
 * 获取SSH密钥信息
 * @returns {Promise<Object>}
 */
export const getSSHKeyInfo = async () => {
  try {
    const res = await sshApi.getSSHKeyInfo();
    return res;
  } catch (error) {
    console.error('获取SSH密钥信息失败:', error);
    throw error;
  }
};

/**
 * 创建SSH密钥
 * @returns {Promise<Object>}
 */
export const createSSHKey = async () => {
  try {
    const res = await sshApi.createSSHKey();
    return res;
  } catch (error) {
    console.error('创建SSH密钥失败:', error);
    throw error;
  }
};

/**
 * 重新创建SSH密钥
 * @returns {Promise<Object>}
 */
export const recreateSSHKey = async () => {
  try {
    const res = await sshApi.recreateSSHKey();
    return res;
  } catch (error) {
    console.error('重新创建SSH密钥失败:', error);
    throw error;
  }
};

/**
 * 下载私钥
 * @param {string} verificationCode - 验证码
 * @returns {Promise<Object>}
 */
export const downloadPrivateKey = async (verificationCode) => {
  try {
    const res = await sshApi.downloadPrivateKey({ verificationCode });
    return res;
  } catch (error) {
    console.error('下载私钥失败:', error);
    throw error;
  }
};

/**
 * 发送验证码
 * @returns {Promise<Object>}
 */
export const sendVerificationCode = async () => {
  try {
    const res = await sshApi.sendVerificationCode();
    return res;
  } catch (error) {
    console.error('发送验证码失败:', error);
    throw error;
  }
};

/**
 * 校验验证码
 * @param {string} verificationCode - 验证码
 * @returns {Promise<Object>}
 */
export const verifyCode = async (verificationCode) => {
  try {
    const res = await sshApi.verifyCode({ verificationCode });
    return res;
  } catch (error) {
    console.error('校验验证码失败:', error);
    throw error;
  }
};

/**
 * 获取资源池SSH状态
 * @returns {Promise<Object>}
 */
export const getPoolSSHStatus = async () => {
  try {
    const res = await sshApi.getPoolSSHStatus();
    return res;
  } catch (error) {
    console.error('获取资源池SSH状态失败:', error);
    throw error;
  }
};

/**
 * 获取功能模块显隐状态
 * @returns {Promise<Object>}
 */
export const getModuleVisibility = async () => {
  try {
    const res = await sshApi.getModuleVisibility();
    return res;
  } catch (error) {
    console.error('获取功能模块显隐状态失败:', error);
    throw error;
  }
};

/**
 * 触发文件下载
 * @param {Blob} blob - 文件数据
 * @param {string} filename - 文件名
 */
export const triggerDownload = (blob, filename) => {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

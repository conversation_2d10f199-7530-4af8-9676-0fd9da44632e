// 运管中心
import RES_DASHBOARD from './resDashboard'; // 运管中心-运营看板
import QUOTA_MANAGE from './quotaManage'; // 运管中心-配额管理
import USAGE_MANAGE from './usageManage'; // 运管中心-用量管理
import ALARM_MANAGE from './alarmManage'; // 运管中心-告警管理
import OPERATION_LOG from './operationLog'; // 运管中心-操作日志
import CORPUS_MANAGE from './corpusManage'; // 运管中心-语料收集管理
import DATA_SECURITY from './dataSecurity'; // 运管中心-数据安全管理
import ACTIVITY_MANAGE from './activityManage'; // 运管中心-项目活动管理
import RESOURCE_GROUP from './resourceGroup'; // 运管中心-资源组管理
import PRESET_ASSET from './presetAsset'; //运管中心-预置资产管理
import STORAGE_GIR_MANAGER from './storageDirManager'; // 运管中心-存储目录管理
import ANNOUNCEMENT from './announcement'; // 运管中心-公告管理

const MANAGEMENT_CENTER = [
  ...RES_DASHBOARD,
  ...QUOTA_MANAGE,
  ...USAGE_MANAGE,
  ...ALARM_MANAGE,
  ...OPERATION_LOG,
  ...CORPUS_MANAGE,
  ...DATA_SECURITY,
  ...ACTIVITY_MANAGE,
  ...RESOURCE_GROUP,
  ...PRESET_ASSET,
  ...STORAGE_GIR_MANAGER,
  ...ANNOUNCEMENT,
];

export default MANAGEMENT_CENTER;

## 5.2.2功能描述

### 1、SSH远程开发入口控制

当资源池已开启"SSH远程开发"且项目空间"数据出口"为开启时，在项目空间详情页，新增"SSH远程开发使用方式"入口，支持项目负责人管理项目空间SSH密钥，支持项目管理员、算法开发（不含服务调用、标注人员）查看项目空间SSH密钥；其他情况下（资源池关闭"SSH远程开发"，或者资源池已开启"SSH远程开发"但项目空间"数据出口"为关闭），不支持SSH密钥的管理和查看，隐藏相关文案及入口。

### 2、点击"查看"显示弹窗

#### （1）当项目空间未创建SSH密钥时：

##### ①针对项目空间负责人，弹窗内容如下

1) 当开发环境功能模块隐藏时，文案仅显示训练任务，当训练任务功能模块隐藏时，文案仅显示开发环境
2) 支持创建密钥，显示对应弹窗（详见后）

##### ②针对项目空间管理员、算法开发人员，弹窗内容如下：

#### （2）当项目空间已创建SSH密钥时：

##### ①针对项目空间负责人，弹窗内容如下

1) 当开发环境功能模块隐藏时，文案仅显示训练任务，当训练任务功能模块隐藏时，文案仅显示开发环境
2) 点击"帮助中心"，新开tab页面，跳转至帮助中心对应说明，url待补充
3) 支持显示SSH密钥指纹及更新时间
4) 支持下载私钥，显示对应弹窗（详见后）
5) 支持重新创建密钥，显示对应弹窗（详见后）

##### ②针对项目空间管理员、算法开发人员，弹窗内容如下：

1) 当开发环境功能模块隐藏时，文案仅显示训练任务，当训练任务功能模块隐藏时，文案仅显示开发环境
2) 点击"帮助中心"，新开tab页面，跳转至帮助中心对应说明，url待补充
3) 支持显示SSH密钥指纹及更新时间
4) 支持下载私钥，显示对应弹窗（详见后）

#### （3）创建密钥：

点击创建密钥，可自动创建SSH密钥

如创建成功，自动下载私钥文件至本地，公钥在服务器端保存，顶部message提示成功，原弹窗内容自动刷新，由未配置密钥弹窗更新为已配置密钥弹窗，弹窗中可正常显示SSH密钥指纹及更新时间

如创建失败，顶部message提示失败，原弹窗内容不变，维持为未配置密钥弹窗

#### （4）重新创建密钥：

点击重新创建密钥，显示二次确认弹窗

点击"确定"，如创建成功，自动下载私钥文件至本地，公钥在服务器端保存，顶部message提示成功，原弹窗内容自动刷新，显示更新后的SSH密钥指纹及更新时间；如创建失败，顶部message提示失败，原弹窗内容不变，维持为更新前的密钥信息

点击"取消"，关闭二次确认弹窗，原弹窗内容不变

#### （5）下载私钥：

点击下载私钥，显示验证码弹窗

点击「获取验证码」，向当前用户发送短信，开始60秒倒计时，倒计时结束后可再次发送短信，短信有效期5分钟；录入验证码并点击「确定」按钮，校验验证码有效性，校验通过，即可自动下载私钥，验证码弹窗关闭，维持原弹窗内容（显示SSH远程开发使用方式）；校验失败，飘红提示"验证码错误"
import { MGT_LOG_VIEW_AUTH } from '@/constants/operationLog';

// 运管中心-操作日志
const OPERATION_LOG = [
  {
    path: '/operation-log',
    name: 'operation-log',
    component: () => import('@/views/management-center/operation-log/index.vue'),
    meta: {
      header: [
        {
          name: '操作日志',
        },
      ],
    },
    children: [
      {
        path: '',
        name: 'ope-log',
        component: () => import('@/views/management-center/operation-log/list/index.vue'),
        meta: {
          header: [
            {
              name: '运管中心',
            },
            {
              name: '操作日志',
            },
          ],
        },
      },
    ],
  },
  // 项目空间操作日志详情
  {
    path: '/operation-log/space-detail/:tab/:id',
    name: 'space-log-detail',
    component: () => import('@/views/management-center/operation-log/detail/index.vue'),
    meta: {
      header: [
        {
          name: '运管中心',
        },
        {
          name: '操作日志',
          path: '/operation-log',
          query: {
            tab: 1,
          },
        },
        {
          name: `操作日志详情`,
        },
      ],
      apvAndMgtPermission: MGT_LOG_VIEW_AUTH,
    },
  },
  // 运管中心操作日志详情
  {
    path: '/operation-log/manage-detail/:tab/:id',
    name: 'manage-log-detail',
    component: () => import('@/views/management-center/operation-log/detail/index.vue'),
    meta: {
      header: [
        {
          name: '运管中心',
        },
        {
          name: '操作日志',
          path: '/operation-log',
          query: {
            tab: 2,
          },
        },
        {
          name: `操作日志详情`,
        },
      ],
      apvAndMgtPermission: MGT_LOG_VIEW_AUTH,
    },
  },
];

export default OPERATION_LOG;

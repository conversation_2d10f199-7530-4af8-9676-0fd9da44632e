// SSH远程开发相关常量

// SSH密钥状态
export const SSH_KEY_STATUS = {
  NOT_CREATED: 'not_created',
  CREATED: 'created'
};

// 用户角色权限映射
export const SSH_ROLE_PERMISSIONS = {
  LEADER: {
    canManage: true,
    canView: true,
    canCreate: true,
    canRecreate: true,
    canDownload: true
  },
  ADMIN: {
    canManage: false,
    canView: true,
    canCreate: false,
    canRecreate: false,
    canDownload: true
  },
  DEVELOP: {
    canManage: false,
    canView: true,
    canCreate: false,
    canRecreate: false,
    canDownload: true
  },
  LABELER: {
    canManage: false,
    canView: false,
    canCreate: false,
    canRecreate: false,
    canDownload: false
  }
};

// 模块显示文案映射
export const MODULE_TEXT_MAP = {
  BOTH_VISIBLE: '您可使用SSH远程开发密钥，在本地通过Xshell、Pycharm、VSCode等工具，连接模型训练（开发环境、训练任务）实例，实现SSH远程开发',
  ONLY_DEV_ENV: '您可使用SSH远程开发密钥，在本地通过Xshell、Pycharm、VSCode等工具，连接模型训练（开发环境）实例，实现SSH远程开发',
  ONLY_TRAIN_TASK: '您可使用SSH远程开发密钥，在本地通过Xshell、Pycharm、VSCode等工具，连接模型训练（训练任务）实例，实现SSH远程开发'
};

// 帮助中心URL（待补充）
export const SSH_HELP_CENTER_URL = '';

// 验证码相关常量
export const VERIFICATION_CODE = {
  COUNTDOWN_TIME: 60, // 倒计时60秒
  VALIDITY_TIME: 5 * 60 * 1000, // 验证码有效期5分钟
  ERROR_MESSAGE: '验证码错误'
};

// 操作成功/失败消息
export const SSH_MESSAGES = {
  CREATE_SUCCESS: 'SSH密钥创建成功',
  CREATE_FAILED: 'SSH密钥创建失败，请稍后再试',
  RECREATE_SUCCESS: 'SSH密钥重新创建成功',
  RECREATE_FAILED: 'SSH密钥重新创建失败，请稍后再试',
  DOWNLOAD_SUCCESS: '私钥下载成功',
  DOWNLOAD_FAILED: '私钥下载失败，请稍后再试',
  VERIFICATION_SEND_SUCCESS: '验证码发送成功',
  VERIFICATION_SEND_FAILED: '验证码发送失败，请稍后再试',
  VERIFICATION_ERROR: '验证码错误',
  PERMISSION_DENIED: '您没有权限执行此操作'
};

<template>
  <div class="container-box">
    <jt-container>
      <sub-header v-if="isAdmin" :bread-crumb="breadCrumb" style="padding-left: 0px"></sub-header>
      <div class="content-title">{{ ProjectBasicInfo.name }}</div>
      <jt-container-item>
        <div class="basic-infor">
          <div class="sub-title">基本信息</div>
          <div v-if="!isAdmin" class="button-class">
            <a-space :size="8">
              <a-button v-if="role === ROLE_KEYS.LEADER" class="exit-class" @click="handleDelete">
                <jt-icon type="iconshanchu1" />
                <span>删除</span>
              </a-button>
              <a-button v-if="role === ROLE_KEYS.ADMIN || role === ROLE_KEYS.DEVELOP" class="exit-class" @click="handleExit">
                <jt-icon type="iconexport" />
                <span>退出</span>
              </a-button>
              <a-button v-if="role === ROLE_KEYS.LEADER || role === ROLE_KEYS.ADMIN" ghost type="primary" @click="handleEdit">
                <jt-icon type="iconbianji" />
                <span>编辑</span>
              </a-button>
            </a-space>
          </div>
        </div>
        <div class="tl-line"></div>
        <div class="sapce-detail">
          <a-descriptions v-if="isAdmin">
            <a-descriptions-item label="项目空间名称">
              {{ ProjectBasicInfo.name }}
            </a-descriptions-item>
            <a-descriptions-item label="负责人">{{ ProjectBasicInfo.leader }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ getFormatTime(ProjectBasicInfo.createTimeStr) }}</a-descriptions-item>
            <a-descriptions-item label="项目空间ID">
              <span>{{ ProjectBasicInfo.id }}</span>
              <span
                ><a-button type="link" class="copy-button" @click="copyLog(ProjectBasicInfo.id)"><jt-icon type="iconfile-copy" /></a-button
              ></span>
            </a-descriptions-item>
            <a-descriptions-item label="关联资源组">
              <type-tag type="public" color="blue" :num="ProjectBasicInfo.publicResGroupNum"></type-tag>
              <type-tag v-if="ProjectBasicInfo.privateResGroupNum > 0" type="privite" color="orange" :num="ProjectBasicInfo.privateResGroupNum"></type-tag>
            </a-descriptions-item>
            <a-descriptions-item>
              <template #label>
                <a-space style="gap: 4px">
                  <a-tooltip overlay-class-name="card-tooltip-wrapper">
                    <template #title>当关闭数据出口时，将不再提供对象存储下载、Jupyter/VSCode、容器内网络代理及对象存储连接、推理服务项目外调用等功能</template>
                    <QuestionCircleOutlined />
                  </a-tooltip>
                  数据出口
                </a-space>
              </template>
              <span :class="['status-icon', ProjectBasicInfo.exposeTotalAccess === 1 && 'open']"></span>
              {{ ProjectBasicInfo.exposeTotalAccess === 1 ? '开启' : '关闭' }}
            </a-descriptions-item>
            <a-descriptions-item label="加速卡平均使用率">{{ formatRateValue(ProjectBasicInfo.gpuCardAverageUsageRate) }}</a-descriptions-item>
            <a-descriptions-item label="加速卡显存平均使用率">{{ formatRateValue(ProjectBasicInfo.gpuCardGMAverageUsageRate) }}</a-descriptions-item>
            <a-descriptions-item label="CPU平均使用率">{{ formatRateValue(ProjectBasicInfo.cpuAverageUsageRate) }}</a-descriptions-item>
            <a-descriptions-item :span="3" label="项目空间描述">{{ ProjectBasicInfo.desc || '--' }}</a-descriptions-item>
          </a-descriptions>
          <template v-else>
            <a-descriptions :label-style="descripLabelStyle">
              <a-descriptions-item label="项目空间名称"> {{ ProjectBasicInfo.name }}</a-descriptions-item>
              <a-descriptions-item label="负责人"> {{ ProjectBasicInfo.leader }}</a-descriptions-item>
              <a-descriptions-item label="创建时间">{{ getFormatTime(ProjectBasicInfo.createTime) }}</a-descriptions-item>
              <a-descriptions-item label="项目空间ID">
                <div>
                  <span>
                    <a-button type="link" class="copy-button" @click="copyLog(ProjectBasicInfo.id)"><jt-icon type="iconfile-copy" /></a-button
                  ></span>
                  <span> {{ ProjectBasicInfo.id }}</span>
                </div>
              </a-descriptions-item>
              <a-descriptions-item label="关联资源组">
                <type-tag type="public" color="blue" :num="ProjectBasicInfo.publicResGroupNum"></type-tag>
                <type-tag v-if="ProjectBasicInfo.privateResGroupNum > 0" type="privite" color="orange" :num="ProjectBasicInfo.privateResGroupNum"></type-tag>
              </a-descriptions-item>
              <a-descriptions-item>
                <template #label>
                  <a-space style="gap: 4px">
                    <a-tooltip overlay-class-name="card-tooltip-wrapper">
                      <template #title>当关闭数据出口时，将不再提供对象存储下载、Jupyter/VSCode、容器内网络代理及对象存储连接、推理服务项目外调用等功能</template>
                      <QuestionCircleOutlined />
                    </a-tooltip>
                    数据出口
                  </a-space>
                </template>
                <span :class="['status-icon', ProjectBasicInfo.exposeTotalAccess === 1 && 'open']"></span>
                {{ ProjectBasicInfo.exposeTotalAccess === 1 ? '开启' : '关闭' }}</a-descriptions-item
              >
              <a-descriptions-item label="项目空间描述">{{ ProjectBasicInfo.desc || '--' }}</a-descriptions-item>
            </a-descriptions>
          </template>
        </div>
      </jt-container-item>
      <jt-container-item>
        <div class="source-control-box">
          <div style="flex: 8">
            <a-tabs v-model:activeKey="activeKey">
              <a-tab-pane key="资源信息" tab="资源信息">
                <resource-information :is-admin="isAdmin" :project-id="projectId"></resource-information>
                <div class="overview-data-box">
                  <div class="overview-title">资源看板</div>
                  <div>
                    <admin-resource-chart v-if="isAdmin" :project-id="projectId" />
                    <resourceChart v-else ref="chartRef" />
                  </div>
                </div>
              </a-tab-pane>
              <a-tab-pane key="成员信息" tab="成员信息">
                <div class="overview-data-box">
                  <project-member ref="projectMemberRef" :is-admin="isAdmin" :project-id="projectId"></project-member>
                </div>
              </a-tab-pane>
              <a-tab-pane key="关联资源组" tab="关联资源组">
                <div class="overview-data-box">
                  <project-resource-group :resource-object="resourceObject" :is-admin="isAdmin" :project-id="projectId"></project-resource-group>
                </div>
              </a-tab-pane>
              <template v-if="!isAdmin" #rightExtra>
                <a-button v-if="activeKey === '关联资源组' && operateRole" ghost type="primary" @click="handleassociateResource">
                  <span style="vertical-align: middle; margin-right: 2px"><img src="@/assets/images/details/assoicon.png" alt="" /> </span><span>关联资源</span>
                </a-button>
                <a-button v-if="activeKey === '资源信息' && operateRole" style="padding-right: 8px" ghost type="primary" @click="handleUpgradeResource">
                  <span>申请扩容</span>
                  <jt-icon type="iconright" />
                </a-button>
                <a-space v-if="activeKey === '成员信息'" class="operation-bar">
                  <jt-search-input v-model="keyword" placeholder="请输入用户名" class="search-input" style="width: 200px" @change="onSearch">
                    <template #prefix>
                      <jt-icon type="iconsousuo" />
                    </template>
                  </jt-search-input>
                  <a-button v-if="operateRole" ghost type="primary" :icon="h(PlusOutlined)" @click="handleAdd">添加成员</a-button>
                </a-space>
              </template>
            </a-tabs>
          </div>
        </div>
      </jt-container-item>
    </jt-container>
    <edit-detail-modal v-if="!isAdmin" :edit-item="editItem" @close="closeEditModal" />
    <delete-detail-modal v-if="!isAdmin" :delete-item="deleteItem" @close="closeDeleteModal" />
    <exit-detail-modal v-if="!isAdmin" :exit-item="exitItem" @close="closeEixtModal" />
  </div>
</template>
<script setup>
import { ref, onMounted, h } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useStore } from 'vuex';
import resourceInformation from './resourceInformation.vue';
import projectMember from './peojectMember.vue';
import projectResourceGroup from './projectResourceGroup.vue';
import editDetailModal from '../component/editSpaceModal.vue';
import deleteDetailModal from '../component/deleteSpaceModal.vue';
import exitDetailModal from '../component/exitSpaceModal.vue';
import { handleCopy, getFormatTime, changePoolAndReload } from '@/utils/index.js';
import resourceChart from './ResourceChart.vue';
import AdminResourceChart from './AdminResourceChart.vue';
import { ROLE_KEYS } from '@/constants/projectSpace.js';
import { projectSpaceApi } from '@/apis';
import { PlusOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import SubHeader from '@/components/subHeader.vue';
import TypeTag from '../component/typeTag.vue';

const totalAccessText = {
  0: '关闭',
  1: '开启',
};

const projectMemberRef = ref();
const router = useRouter();
const route = useRoute();
const store = useStore();

const chartRef = ref(null);
// 运管页面的项目空间详情和用户侧相同，使用admin参数进行判断
const isAdmin = route.meta.admin;
const breadCrumb = route.meta.header;
// 运管侧没有进入具体的项目，projectId从接口参数传
const projectId = route.params.projectId;

const activeKey = ref('资源信息');
const resourceObject = ref({});

const descripLabelStyle = {
  justifyContent: 'flex-end',
  minWidth: '100px',
};

//搜索keyword
const keyword = ref('');
const onSearch = () => {
  projectMemberRef.value.onSearch(keyword);
};
//项目空间详情
const ProjectBasicInfo = ref({});
//当前项目角色
const roleInfo = ref([]);

onMounted(() => {
  if (route.query.activeKey) {
    activeKey.value = route.query.activeKey;
  }
  getCurrentRole();
  getProjectDetail();
});

const role = ref('');

const operateRole = computed(() => {
  return role.value === ROLE_KEYS.LEADER || role.value === ROLE_KEYS.ADMIN;
});

watch(
  () => store.state.projectId,
  (val) => {
    if (val) {
      getCurrentRole();
      getProjectDetail();
      chartRef.value.geteDiagramData();
      chartRef.value.initChart();
    }
  }
);

const formatRateValue = (val) => {
  return val === undefined ? '--' : `${val}%`;
};

//添加项目成员
const handleAdd = () => {
  projectMemberRef.value.addItemVisible.visible = true;
};

//获取当前项目中的角色
const getCurrentRole = async () => {
  const res = await projectSpaceApi.getProjectRole();
  if (res.code === 0) {
    roleInfo.value = res.data || [];
    const temp = roleInfo.value.map((e) => {
      return e.key;
    });
    if (temp.indexOf(ROLE_KEYS.LEADER) !== -1) {
      role.value = ROLE_KEYS.LEADER;
    } else if (temp.indexOf(ROLE_KEYS.ADMIN) !== -1) {
      role.value = ROLE_KEYS.ADMIN;
    } else {
      role.value = ROLE_KEYS.DEVELOP;
    }
  }
};
//获取项目空间详情
const getProjectDetail = async () => {
  const requestMethod = isAdmin ? projectSpaceApi.getAdminProjectDetail : projectSpaceApi.getProjectDetail;
  const res = await requestMethod({ projectId });
  if (res.code === 0) {
    ProjectBasicInfo.value = res.data || {};
  } else {
    ProjectBasicInfo.value = {};
    if (res.code === 130209) {
      message.error('项目空间不存在');
    }
  }
};

//项目空间编辑
const editItem = ref({
  id: '',
  name: '',
  desc: '',
  visible: false,
});
//删除
const deleteItem = ref({
  id: '',
  name: '',
  visible: '',
});
const exitItem = ref({
  id: '',
  name: '',
  visible: '',
});
//编辑项目空间
const handleEdit = () => {
  editItem.value.name = ProjectBasicInfo.value.name;
  editItem.value.desc = ProjectBasicInfo.value.desc;
  editItem.value.id = store.state.projectId;
  editItem.value.visible = true;
};
const closeEditModal = (bol) => {
  editItem.value.visible = false;
  if (bol) {
    getProjectDetail();
  }
};
//删除项目目空间
const handleDelete = () => {
  deleteItem.value.name = ProjectBasicInfo.value.name;
  deleteItem.value.id = store.state.projectId;
  deleteItem.value.visible = true;
};
const closeDeleteModal = (deleted) => {
  deleteItem.value.id = '';
  deleteItem.value.name = '';
  deleteItem.value.visible = false;
  if (deleted) {
    changePoolAndReload(store.state.poolInfo.id, '#/project-space');
  }
};
//退出项目空间
const handleExit = () => {
  exitItem.value.name = ProjectBasicInfo.value.name;
  exitItem.value.id = store.state.projectId;
  exitItem.value.visible = true;
};
const closeEixtModal = (exited) => {
  exitItem.value.visible = false;
  if (exited) {
    changePoolAndReload(store.state.poolInfo.id, '#/project-space');
  }
};
//关联资源
const handleassociateResource = () => {
  router.push('/project-space-details/associate-resource');
};
const handleUpgradeResource = () => {
  router.push('/project-space-details/upgrade-resource');
};
//复制id
const copyLog = (value) => {
  handleCopy(value);
};
</script>
<style lang="less" scoped>
.container-box {
  margin: -20px 0 0;
  .basic-infor {
    display: flex;
    align-items: baseline;
    margin-top: -4px;
    .sub-title {
      flex: 1;
      font-size: 16px;
      font-weight: 600;
      color: #00141a;
      margin-bottom: 16px;
    }
    .button-class :deep(.ant-btn) {
      width: 82px;
      height: 32px;
    }
  }
  .tl-line {
    height: 1px;
    margin-bottom: 23px;
    background: rgba(0, 20, 26, 0.08);
  }
  .sapce-detail {
    margin: 20px 0 0 0px;
    .space-name {
      display: flex;
      margin-top: 40px;
    }
    .space-describe {
      margin-top: 40px;
      padding-bottom: 50px;
    }
    .status-icon {
      width: 6px;
      height: 6px;
      margin-right: 8px;
      border-radius: 50%;
      background-color: @jt-text-color-primary-opacity045;
      &.open {
        background-color: @jt-primary-color;
      }
    }
  }
}
.sapce-detail :deep(.ant-descriptions-item-content) {
  align-items: center;
  // align-items: baseline;
}
.sapce-detail .copy-button {
  color: rgba(0, 20, 26, 0.45);
  padding: 0 4px;
  height: 0;
  // margin-left: 8px;
}
.sapce-detail .copy-button:hover {
  color: #00a0cc;
}
.source-control-box {
  background-color: #fff;
  border-radius: 4px;
  .title-box {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    .title {
      font-size: 16px;
      font-weight: 600;
      color: #121f2c;
    }
  }

  /* // tab */
  :deep(.ant-tabs-tab) {
    font-size: 16px;
    font-weight: 400;
    height: 35px;
    padding: 3px 12px;
    color: rgba(0, 20, 26, 0.7);
    font-family: PingFangSC-Regular, PingFang SC, sans-serif;
  }
  .overview-data img {
    width: 96px;
  }
  /* // tab面板中的数据展示 */
  .overview-data-box {
    box-sizing: border-box;
    margin-bottom: 30px;
    .overview-title {
      font-weight: 600;
      color: #121f2c;
      display: flex;
      align-items: center;
      // margin-bottom: 20px;
      font-size: 14px;
    }
    .overview-title::before {
      content: '';
      width: 4px;
      height: 14px;
      background-color: @jt-primary-color;
      margin-right: 8px;
      /* // transform: translateY(1px); */
    }

    /* // 日常指标等数据 */
    .overview-data-item-box {
      box-sizing: border-box;
      align-items: center;
      display: flex;
    }
  }
}
.stroage-used-modal {
  :deep(.ant-modal-body) {
    font-size: 14px;
    line-height: 32px;
    padding: 32px 24px;
  }
}
:deep(.ant-tabs .ant-tabs-extra-content) {
  margin-bottom: 12px;
}
.exit-class:hover {
  color: #f53922;
  border-color: #f53922;
  box-shadow: none;
}
</style>

// 运管中心-预置资产管理
import { PRESET_ASSETS_VIEW_AUTH, PRESET_ASSETS_EDIT_AUTH } from '@/constants/presetAssets';
const PRESET_ASSET = [
  {
    path: '/preset-asset',
    name: 'preset-asset',
    component: () => import('@/views/management-center/preset-asset/index.vue'),
  },
  {
    path: '/preset-asset/preset-model-create',
    name: 'preset-model-create',
    component: () => import('@/views/management-center/preset-asset/preset-model-create/index.vue'),
    meta: {
      apvAndMgtPermission: PRESET_ASSETS_EDIT_AUTH,
    },
  },
  {
    path: '/preset-asset/preset-model-detail',
    name: 'preset-model-detail',
    component: () => import('@/views/management-center/preset-asset/preset-model-detail/index.vue'),
    meta: {
      apvAndMgtPermission: PRESET_ASSETS_VIEW_AUTH,
    },
  },
  /* 预置镜像 */
  {
    path: '/preset-asset/preset-mirror-create',
    name: 'preset-mirror-create',
    component: () => import('@/views/management-center/preset-asset/mirror/create-form/index.vue'),
    meta: {
      apvAndMgtPermission: PRESET_ASSETS_EDIT_AUTH,
    },
  },
  {
    path: '/preset-asset/preset-mirror-edit/:id',
    name: 'preset-mirror-edit',
    component: () => import('@/views/management-center/preset-asset/mirror/create-form/index.vue'),
    meta: {
      apvAndMgtPermission: PRESET_ASSETS_EDIT_AUTH,
      isEdit: true,
    },
  },
  {
    path: '/preset-asset/preset-mirror-detail/:id',
    name: 'preset-mirror-detail',
    component: () => import('@/views/management-center/preset-asset/mirror/detail/index.vue'),
    meta: {
      apvAndMgtPermission: PRESET_ASSETS_VIEW_AUTH,
    },
  },
];

export default PRESET_ASSET;

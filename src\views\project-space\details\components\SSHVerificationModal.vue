<template>
  <a-modal
    v-model:open="visible"
    title="编组11备份"
    :width="480"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="verification-content">
      <p class="verification-description">
        为保护项目空间隐私信息，我们会向您登录平台的手机号码发送短信验证码，请先验证后再下载私钥。
      </p>
      
      <div class="verification-form">
        <div class="form-item">
          <span class="form-label">短信验证</span>
          <div class="form-input-group">
            <a-input
              v-model:value="verificationCode"
              placeholder="请输入验证码"
              :status="inputStatus"
              @change="handleInputChange"
            />
            <a-button
              type="primary"
              :disabled="countdown > 0"
              @click="handleSendCode"
            >
              {{ countdown > 0 ? `${countdown}s` : '获取验证码' }}
            </a-button>
          </div>
        </div>
        
        <div v-if="errorMessage" class="error-message">
          {{ errorMessage }}
        </div>
      </div>
      
      <div class="verification-actions">
        <a-button @click="handleCancel">取消</a-button>
        <a-button
          type="primary"
          :disabled="!verificationCode.trim()"
          @click="handleConfirm"
        >
          确定
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch, onBeforeUnmount } from 'vue';
import { message } from 'ant-design-vue';
import { VERIFICATION_CODE, SSH_MESSAGES } from '@/constants/ssh';
import { sendVerificationCode } from '../sshUtils';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'verify']);

const verificationCode = ref('');
const countdown = ref(0);
const errorMessage = ref('');
const inputStatus = ref('');

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

let countdownTimer = null;

const handleCancel = () => {
  resetForm();
  visible.value = false;
};

const handleConfirm = () => {
  if (!verificationCode.value.trim()) {
    errorMessage.value = '请输入验证码';
    inputStatus.value = 'error';
    return;
  }
  
  emit('verify', verificationCode.value.trim());
};

const handleSendCode = async () => {
  try {
    const res = await sendVerificationCode();
    if (res.code === 0) {
      message.success(SSH_MESSAGES.VERIFICATION_SEND_SUCCESS);
      startCountdown();
    } else {
      message.error(res.message || SSH_MESSAGES.VERIFICATION_SEND_FAILED);
    }
  } catch (error) {
    message.error(SSH_MESSAGES.VERIFICATION_SEND_FAILED);
  }
};

const startCountdown = () => {
  countdown.value = VERIFICATION_CODE.COUNTDOWN_TIME;
  countdownTimer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(countdownTimer);
      countdownTimer = null;
    }
  }, 1000);
};

const handleInputChange = () => {
  if (errorMessage.value) {
    errorMessage.value = '';
    inputStatus.value = '';
  }
};

const resetForm = () => {
  verificationCode.value = '';
  errorMessage.value = '';
  inputStatus.value = '';
  if (countdownTimer) {
    clearInterval(countdownTimer);
    countdownTimer = null;
  }
  countdown.value = 0;
};

// 监听弹窗关闭，重置表单
watch(visible, (newVal) => {
  if (!newVal) {
    resetForm();
  }
});

// 组件卸载时清理定时器
onBeforeUnmount(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
});
</script>

<style lang="less" scoped>
.verification-content {
  padding: 16px 0;

  .verification-description {
    margin-bottom: 24px;
    line-height: 1.6;
    color: rgba(0, 20, 26, 0.85);
  }

  .verification-form {
    margin-bottom: 24px;

    .form-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 16px;

      .form-label {
        min-width: 80px;
        padding-top: 6px;
        color: rgba(0, 20, 26, 0.85);
      }

      .form-input-group {
        flex: 1;
        display: flex;
        gap: 12px;

        .ant-input {
          flex: 1;
        }

        .ant-btn {
          white-space: nowrap;
        }
      }
    }

    .error-message {
      color: #ff4d4f;
      font-size: 14px;
      margin-top: 8px;
    }
  }

  .verification-actions {
    text-align: center;

    .ant-btn {
      margin: 0 8px;
    }
  }
}
</style>

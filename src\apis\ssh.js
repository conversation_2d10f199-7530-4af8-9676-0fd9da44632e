import { GET, POST, requestWithPoolId, requestWithProjectId } from '@/request';
const { GET: requestWithProjectIdGET, POST: requestWithProjectIdPOST } = requestWithProjectId;
const { POST: requestWithPoolIdPOST, GET: requestWithPoolIdGET } = requestWithPoolId;

// 获取SSH密钥信息
export const getSSHKeyInfo = () => requestWithProjectIdGET('/web/project/v1/ssh/key/info');

// 创建SSH密钥
export const createSSHKey = () => requestWithProjectIdPOST('/web/project/v1/ssh/key/create');

// 重新创建SSH密钥
export const recreateSSHKey = () => requestWithProjectIdPOST('/web/project/v1/ssh/key/recreate');

// 下载私钥
export const downloadPrivateKey = (data) => requestWithProjectIdPOST('/web/project/v1/ssh/key/download', data);

// 发送验证码
export const sendVerificationCode = () => requestWithProjectIdPOST('/web/project/v1/ssh/verification-code/send');

// 校验验证码
export const verifyCode = (data) => requestWithProjectIdPOST('/web/project/v1/ssh/verification-code/verify', data);

// 获取资源池SSH远程开发状态
export const getPoolSSHStatus = () => requestWithPoolIdGET('/web/resource/v1/pool/ssh/status');

// 获取功能模块显隐状态
export const getModuleVisibility = () => requestWithProjectIdGET('/web/project/v1/module/visibility');

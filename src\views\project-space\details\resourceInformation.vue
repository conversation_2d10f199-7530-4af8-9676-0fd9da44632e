<template>
  <div class="overview-data-box">
    <div class="resource-overview">
      <div class="overview-title">资源概览</div>
    </div>
    <div class="overview-data">
      <div class="overview-data-content">
        <div class="overview-data-item">
          <div><img src="@/assets/images/details/gpucard.png" alt="" /></div>
          <div class="overview-data-item-title">加速卡资源</div>
          <div class="overview-data-item-content">
            <div class="text-weight">
              <span class="overview-data-used">{{ acce_card_used }}</span> / {{ acce_card_total }}卡
            </div>
            <div>
              <div class="data-text">已用 / 总量</div>
              <div style="display: inline-block">
                <a-tooltip placement="top" trigger="hover" overlay-class-name="gpu-resource-tooltip-wrapper">
                  <template #title>
                    <div class="tooltip-name">
                      <div class="info-title">加速卡资源</div>
                      <div class="typespan">
                        针对加速卡类型资源组最多可使用<span class="font-color">{{ acce_card_total }}</span
                        >卡，当前已使用<span class="font-color">{{ acce_card_used }}</span
                        >卡
                      </div>
                    </div>
                  </template>
                  <InfoCircleOutlined class="data-icon" />
                </a-tooltip>
              </div>
            </div>
          </div>
        </div>
        <div class="overview-data-item">
          <div><img src="@/assets/images/details/cpucard.png" alt="" /></div>
          <div class="overview-data-item-title">CPU资源</div>
          <div class="overview-data-item-content">
            <div class="text-weight">
              <span class="overview-data-used">{{ cpu_used }}</span> / {{ cpu_total }}核
            </div>
            <div>
              <div class="data-text">已用 / 总量</div>
              <div style="display: inline-block">
                <a-tooltip placement="top" trigger="hover" overlay-class-name="gpu-resource-tooltip-wrapper">
                  <template #title>
                    <div class="tooltip-name">
                      <div class="info-title">CPU资源</div>
                      <div class="typespan">
                        针对CPU类型资源组，最多可使用<span class="font-color">{{ cpu_total }}</span
                        >核,当前已使用<span class="font-color">{{ cpu_used }}核</span>
                      </div>
                    </div>
                  </template>
                  <InfoCircleOutlined class="data-icon" />
                </a-tooltip>
              </div>
            </div>
          </div>
        </div>
        <div class="overview-data-item">
          <div><img src="@/assets/images/details/imagestorage.png" alt="" /></div>
          <div class="overview-data-item-title">镜像资源</div>
          <div class="overview-data-item-content">
            <div class="text-weight">
              <p>
                <span class="overview-data-used">{{ image_used }}</span
                ><span>{{ image_used_unit }}</span> / <span>{{ image_total }}{{ image_total_unit }}</span>
              </p>
            </div>
            <div class="data-text">已用 / 总量</div>
          </div>
        </div>
      </div>
      <div class="overview-data-content" style="margin-top: 20px">
        <div class="overview-data-item">
          <div><img src="@/assets/images/details/norstorage.png" alt="" /></div>
          <div class="overview-data-item-title">普通<br />文件存储</div>
          <div class="overview-data-item-content">
            <div class="text-weight">
              <p>
                <span class="overview-data-used">{{ general_stor_used }}</span
                ><span>{{ general_stor_used_unit }}</span> / <span>{{ general_stor_total }}{{ general_stor_total_unit }}</span>
              </p>
            </div>
            <div>
              <div class="data-text">已用 / 总量</div>
              <div style="display: inline-block">
                <a-tooltip placement="top" :overlay-style="{ width: '200px' }" :title="generalStorageTooltip">
                  <InfoCircleOutlined class="data-icon" />
                </a-tooltip>
              </div>
            </div>
          </div>
        </div>
        <div class="overview-data-item">
          <div><img src="@/assets/images/details/hgstorage.png" alt="" /></div>
          <div class="overview-data-item-title">高性能<br />文件存储</div>
          <div class="overview-data-item-content">
            <div class="text-weight">
              <p>
                <span class="overview-data-used">{{ hs_stor_used }}</span
                ><span>{{ hs_stor_used_unit }}</span> / <span>{{ hs_stor_total }}{{ hs_stor_total_unit }}</span>
              </p>
            </div>
            <div>
              <div class="data-text">已用 / 总量</div>
              <div style="display: inline-block">
                <a-tooltip placement="top" :overlay-style="{ width: '200px' }" :title="highPerformanceStorageTooltip">
                  <InfoCircleOutlined class="data-icon" />
                </a-tooltip>
              </div>
            </div>
          </div>
        </div>
        <div class="overview-data-item">
          <div><img src="@/assets/images/details/objstorage.png" alt="" /></div>
          <div class="overview-data-item-title">对象存储</div>
          <div class="overview-data-item-content">
            <div class="text-weight">
              <p>
                <span class="overview-data-used">{{ object_stor_used }}</span
                ><sapn>{{ object_stor_used_unit }}</sapn> / <span>{{ object_stor_total }}{{ object_stor_total_unit }}</span>
              </p>
            </div>
            <div class="data-text">已用 / 总量</div>
          </div>
        </div>
      </div>
      <div class="alert-tips">
        <a-alert type="info" show-icon>
          <template #message>
            <div class="flex">
              <p><span>加速卡资源占用详情</span> <a-button class="chakan" type="link" @click="onCheckUsedDetail('gpucard')">查看</a-button></p>
              <p><span class="span-line"></span><span>CPU资源占用详情</span><a-button class="chakan" type="link" @click="onCheckUsedDetail('cpu')">查看</a-button></p>
              <p><span class="span-line"></span><span>对象存储使用方式</span><a-button class="chakan" type="link" @click="() => (showLLMDescription = true)">查看</a-button></p>
              <p v-if="supplyPlatformSwitch && !exposeTotalAccess"><span class="span-line"></span><span>高质量数据供给和管理平台存储使用方式</span><a-button class="chakan" type="link" @click="checkHqdsmpStore">查看</a-button></p>
              <p v-if="showSSHEntry"><span class="span-line"></span><span>SSH远程开发使用方式</span><a-button class="chakan" type="link" @click="handleSSHClick">查看</a-button></p>
            </div>
          </template>
        </a-alert>
      </div>
    </div>
    <!-- 加速卡资源占用情况 -->
    <a-modal v-model:open="showaCardVisible" title="加速卡资源占用详情" :width="556" class="card-modal" @cancel="handleCancel" @ok="() => (showaCardVisible = false)">
      <div v-if="modelOccupancyList && modelOccupancyList.length" class="card-detail">
        <div class="card-detail-name">加速卡占用类型</div>
        <a-descriptions class="card-detail-info">
          <a-descriptions-item v-for="item in modelOccupancyList" :key="item.name" :label="item.name" class="info-item">{{ item.value }}卡</a-descriptions-item>
        </a-descriptions>
      </div>
      <div v-if="activityOccupancyList && activityOccupancyList.length" class="card-detail">
        <div class="card-detail-name">项目活动占用类型</div>
        <a-descriptions class="card-detail-info">
          <a-descriptions-item v-for="item in activityOccupancyList" :key="item.name" :label="item.name" class="info-item">{{ item.value }}卡</a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
    <!-- cpu占用详情 -->
    <a-modal v-model:open="showCPUOccpuyDisable" title="CPU资源占用详情" class="card-modal" @cancel="handleCancel" @ok="() => (showCPUOccpuyDisable = false)">
      <div v-if="activityOccupancyList && activityOccupancyList.length" class="card-detail">
        <div class="card-detail-name">项目活动占用类型</div>
        <a-descriptions class="card-detail-info">
          <a-descriptions-item v-for="item in activityOccupancyList" :key="item.name" :label="item.name" class="info-item">{{ item.value }}核</a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
    <!-- 对象使用存储说明 -->
    <a-modal v-model:open="showLLMDescription" title="对象存储使用方式说明" width="470px" class="stroage-used-modal" @cancel="handleCancel" @ok="() => (showLLMDescription = false)">
      <div class="stroage-name">
        <div>您可使用S3 Browser对象存储客户端，将文件上传至当前项目空间</div>
        <div v-if="!isEntranceBySasac()" style="margin-bottom: 10px">如需更多帮助，可前往帮助中心查看 <a @click="jumpToGuideUrl">操作指引</a></div>
        <div>
          <span class="stroage-name-title">Endpoint：</span><span>{{ credentials.endpoint }}</span>
          <a-button type="link" class="copy-button" @click="copyLog(credentials.endpoint)"><jt-icon type="iconfile-copy" /></a-button>
        </div>
        <div>
          <span class="stroage-name-title">Access Key ID：</span><span>{{ credentials.accessKey }}</span>
          <a-button type="link" class="copy-button" @click="copyLog(credentials.accessKey)"><jt-icon type="iconfile-copy" /></a-button>
        </div>
        <div>
          <span class="stroage-name-title">Secret Access Key：</span><span>***</span>
          <a-button type="link" class="copy-button" @click="copyLog(credentials.secretKey)"><jt-icon type="iconfile-copy" /></a-button>
        </div>
      </div>
    </a-modal>
    <MountHqdsmpStoreDlg v-model:visible="showHqdsmpStoreDlg" />
    <UnmountHqdsmpStoreDlg v-model:visible="showUnmountHqdsmpStoreDlg" :project-id="props.projectId" />
    <SSHRemoteDevModal v-model:visible="showSSHModal" />
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps, computed } from 'vue';
import { message } from 'ant-design-vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import { handleCopy, isEntranceBySasac } from '@/utils/index.js';
import { projectSpaceApi, resourceApi, storageApi } from '@/apis';
import { dataToSizeConversion } from '@/utils/storage.js';
import { useStore } from 'vuex';
import { getEnvConfig } from '@/config';
import { generalStorageTooltip, highPerformanceStorageTooltip } from '../const';
import { openInNewTab } from '@/utils';
import { requestWithProjectId } from '@/request';
import MountHqdsmpStoreDlg from './components/MountHqdsmpStoreDlg.vue';
import UnmountHqdsmpStoreDlg from './components/UnmountHqdsmpStoreDlg.vue';
import SSHRemoteDevModal from './components/SSHRemoteDevModal.vue';
import { checkResourceAuth } from '@/utils/auth';
import { AUTH_CHECKS } from '@/constants/management-platform/poolManage';
import { shouldShowSSHEntry, getPoolSSHStatus } from './sshUtils';
import { getCurrentRole } from './utils';

const supplyPlatformSwitch = computed(() => checkResourceAuth('supplyPlatformSwitch')); // 是否开启对接高质量数据供给平台
const showHqdsmpStoreDlg = ref(false);
const showUnmountHqdsmpStoreDlg = ref(false);

// SSH相关状态
const showSSHModal = ref(false);
const poolSSHEnabled = ref(false);
const userRole = ref('');

// SSH入口显示控制
const showSSHEntry = computed(() => {
  return shouldShowSSHEntry(
    poolSSHEnabled.value,
    !exposeTotalAccess.value, // 数据出口开启时exposeTotalAccess为false
    userRole.value
  );
});

const props = defineProps({
  isAdmin: {
    type: Boolean,
    default: false,
  },
  projectId: {
    type: String,
    default: undefined,
  },
});

const store = useStore();
const exposeTotalAccess = computed(() => store.state.projectInfo.exposeTotalAccess);

//资源概览数据
const resourceOverviewData = ref({
  acce_card: {},
  cpu: {},
  general_stor: {},
  image: {},
  object_stor: {},
});

//获取配额加速卡使用详情
const modelOccupancyList = ref([]); // 加速卡占用类型
// 项目活动占用类型
const activityOccupancyList = ref([]);
const showaCardVisible = ref(false); // 加速卡资源占用情况
const showCPUOccpuyDisable = ref(false); // cpu占用情况
const showLLMDescription = ref(false); // 对象存储使用方式
//存储对象使用说明data
const credentials = ref({
  endpoint: '',
  secretKey: '',
  accessKey: '',
});

const quotaType = ref('');
const onCheckUsedDetail = (key) => {
  quotaType.value = key;
  //加速卡使用详情
  getQuotaUsed();
  if (key == 'cpu') {
    showCPUOccpuyDisable.value = true;
  } else if (key == 'gpucard') {
    showaCardVisible.value = true;
  }
};

onMounted(() => {
  //使用配额情况
  getQuotaStatus();
  getResourceCredentials();
  // 初始化SSH相关状态
  initSSHStatus();
});

//获取项目配额加速卡使用详情
const getQuotaUsed = async () => {
  const requestMethod = props.isAdmin ? resourceApi.getAdminUsedDetails : resourceApi.getUsedDetails;
  const res = await requestMethod({
    quotaType: quotaType.value,
    projectId: props.projectId,
  });
  if (res.code === 0) {
    activityOccupancyList.value = res.data.activityOccupancyList.filter((v) => {
      return AUTH_CHECKS.every((check) => checkResourceAuth(check.auth) || v.name !== check.value);
    });
    modelOccupancyList.value = res.data.modelOccupancyList;
  }
};

//获取对象存储认证信息
const getResourceCredentials = async () => {
  const requestMethod = props.isAdmin ? storageApi.getAdminCrefentials : storageApi.getcredentials;
  const res = await requestMethod({ projectId: props.projectId });
  if (res.code === 0) {
    credentials.value = res.data;
  }
};

//获取当前项目配额使用情况
const getQuotaStatus = async () => {
  const requestMethod = props.isAdmin ? projectSpaceApi.getAdminQuotaStatus : projectSpaceApi.getQuotaStatus;

  const res = await requestMethod({ projectId: props.projectId });
  let temp = {};
  if (res.code === 0) {
    for (const key in res.data) {
      if (key == 'acce_card' || key == 'cpu') {
        temp[key] = {
          total: res.data[key].total,
          totalUnit: '',
          used: res.data[key].used,
          usedUnit: '',
        };
      } else {
        temp[key] = {
          total: res.data[key].total > 1024 ? dataToSizeConversion(res.data[key].total).sizeTxt : res.data[key].total,
          totalUnit: res.data[key].total > 1024 ? dataToSizeConversion(res.data[key].total).units : '',
          used: res.data[key].used > 1024 ? dataToSizeConversion(res.data[key].used).sizeTxt : res.data[key].used,
          usedUnit: res.data[key].total > 1024 ? dataToSizeConversion(res.data[key].used).units : '',
        };
      }
    }
    resourceOverviewData.value = temp;
  } else {
    message.error('当前项目配额使用获取失败,请稍后再试');
  }
};
const acce_card_used = computed(() => {
  return resourceOverviewData.value?.acce_card?.used === 0 ? '0' : resourceOverviewData.value?.acce_card?.used ? resourceOverviewData.value?.acce_card?.used : '--';
});
const acce_card_total = computed(() => {
  return resourceOverviewData.value?.acce_card?.total === 0 ? '0' : resourceOverviewData.value?.acce_card?.total ? resourceOverviewData.value?.acce_card?.total : '--';
});
const cpu_used = computed(() => {
  return resourceOverviewData.value?.cpu?.used === 0 ? '0' : resourceOverviewData.value?.cpu?.used ? resourceOverviewData.value?.cpu?.used : '--';
});
const cpu_total = computed(() => {
  return resourceOverviewData.value?.cpu?.total === 0 ? '0' : resourceOverviewData.value?.cpu?.total ? resourceOverviewData.value?.cpu?.total : '--';
});
//镜像资源存储
const image_used = computed(() => {
  return resourceOverviewData.value?.image?.used === 0 ? '0' : resourceOverviewData.value?.image?.used ? resourceOverviewData.value?.image?.used : '--';
});
const image_used_unit = computed(() => {
  return resourceOverviewData.value?.image?.used === 0 ? '' : resourceOverviewData.value?.image?.used ? resourceOverviewData.value?.image?.usedUnit : '';
});
const image_total = computed(() => {
  return resourceOverviewData.value?.image?.total === 0 ? '0' : resourceOverviewData.value?.image?.total ? resourceOverviewData.value?.image?.total : '--';
});
const image_total_unit = computed(() => {
  return resourceOverviewData.value?.image?.total === 0 ? '' : resourceOverviewData.value?.image?.total ? resourceOverviewData.value?.image?.totalUnit : '';
});
//普通文件存储
const general_stor_used = computed(() => {
  return resourceOverviewData.value?.general_stor?.used === 0 ? '0' : resourceOverviewData.value?.general_stor?.used ? resourceOverviewData.value?.general_stor?.used : '--';
});
const general_stor_used_unit = computed(() => {
  return resourceOverviewData.value?.general_stor?.used === 0 ? '' : resourceOverviewData.value?.general_stor?.used ? resourceOverviewData.value?.general_stor?.usedUnit : '';
});
const general_stor_total = computed(() => {
  return resourceOverviewData.value?.general_stor?.total === 0 ? '0' : resourceOverviewData.value?.general_stor?.total ? resourceOverviewData.value?.general_stor?.total : '--';
});
const general_stor_total_unit = computed(() => {
  return resourceOverviewData.value?.general_stor?.total === 0 ? '' : resourceOverviewData.value?.general_stor?.total ? resourceOverviewData.value?.general_stor?.totalUnit : '';
});
//高性能存储
const hs_stor_used = computed(() => {
  return resourceOverviewData.value?.hs_stor?.used === 0 ? '0' : resourceOverviewData.value?.hs_stor?.used ? resourceOverviewData.value?.hs_stor?.used : '--';
});
const hs_stor_used_unit = computed(() => {
  return resourceOverviewData.value?.hs_stor?.used === 0 ? '' : resourceOverviewData.value?.hs_stor?.used ? resourceOverviewData.value?.hs_stor?.usedUnit : '';
});
const hs_stor_total = computed(() => {
  return resourceOverviewData.value?.hs_stor?.total === 0 ? '0' : resourceOverviewData.value?.hs_stor?.total ? resourceOverviewData.value?.hs_stor?.total : '--';
});
const hs_stor_total_unit = computed(() => {
  return resourceOverviewData.value?.hs_stor?.total === 0 ? '' : resourceOverviewData.value?.hs_stor?.total ? resourceOverviewData.value?.hs_stor?.totalUnit : '';
});
//对象存储
const object_stor_used = computed(() => {
  return resourceOverviewData.value?.object_stor?.used === 0 ? '0' : resourceOverviewData.value?.object_stor?.used ? resourceOverviewData.value?.object_stor?.used : '--';
});
const object_stor_used_unit = computed(() => {
  return resourceOverviewData.value?.object_stor?.used === 0 ? '' : resourceOverviewData.value?.object_stor?.used ? resourceOverviewData.value?.object_stor?.usedUnit : '';
});
const object_stor_total = computed(() => {
  return resourceOverviewData.value?.object_stor?.total === 0 ? '' : resourceOverviewData.value?.object_stor?.total ? resourceOverviewData.value?.object_stor?.total : '--';
});
const object_stor_total_unit = computed(() => {
  return resourceOverviewData.value?.object_stor?.total === 0 ? '' : resourceOverviewData.value?.object_stor?.total ? resourceOverviewData.value?.object_stor?.totalUnit : '';
});

watch(
  () => store.state.projectId,
  (val) => {
    if (val) {
      //使用配额情况
      getQuotaStatus();
      getResourceCredentials();
    }
  }
);
//对象存储使用copy
const copyLog = (value) => {
  handleCopy(value);
};

const objectStorageGuideUrl = computed(() => {
  return getEnvConfig('OBJECT_STORAGE_GUIDE_URL');
});

const jumpToGuideUrl = () => {
  openInNewTab(objectStorageGuideUrl.value);
};

const checkHqdsmpStore = async () => {
  try {
    const res = await requestWithProjectId.GET('/web/project/v1/external/status');
    if (res.code === 0) {
      // 0是未绑定，1是已绑定
      if (res.data === 0) {
        showHqdsmpStoreDlg.value = true;
      } else {
        showUnmountHqdsmpStoreDlg.value = true;
      }
    } else {
      message.error('获取绑定状态失败，请稍后再试');
    }
  } catch (error) {
    message.error('获取绑定状态失败，请稍后再试');
  }
};

// SSH相关方法
const initSSHStatus = async () => {
  try {
    // 获取用户角色
    const { role } = await getCurrentRole();
    userRole.value = role;

    // 获取资源池SSH状态
    const poolRes = await getPoolSSHStatus();
    if (poolRes.code === 0) {
      poolSSHEnabled.value = poolRes.data.enabled;
    }
  } catch (error) {
    console.error('初始化SSH状态失败:', error);
  }
};

const handleSSHClick = () => {
  showSSHModal.value = true;
};
</script>

<style lang="less" scoped>
.overview-data img {
  width: 56px;
}
.alert-tips {
  margin: 24px 0px;
  .ant-alert {
    height: 40px;
  }
  .chakan {
    padding: 0 0 0 8px;
  }
}
.card-modal {
  .detail-name {
    display: flex;
    flex-wrap: wrap;
    p {
      width: 50%;
    }
  }
}

:where(.css-dev-only-do-not-override-uziyhx).ant-modal .ant-modal-footer {
  margin-top: 28px;
}
.overview-data .overview-data-content {
  display: flex;
  flex-direction: row;
  .overview-data-item {
    flex: 1;
    display: flex;
    align-items: center;
    min-width: 33%;
    .overview-data-item-title {
      margin: 0 12px;
      font-size: 16px;
      color: #00141a;
      width: 80px;
    }
    .overview-data-item-content {
      display: flex;
      flex-direction: column;
      .overview-data-used {
        font-size: 28px;
        font-weight: 600;
        color: #00141a;
      }
    }
  }
}
.data-text {
  font-size: 14px;
  display: inline-block;
  color: rgba(0, 20, 26, 0.45);
  margin-right: 9px;
  font-family: PingFangSC-Regular, PingFang SC, sans-serif;
}
.data-icon {
  width: 16px;
  color: rgba(0, 20, 26, 0.45);
}
//存储对象使用说明
.stroage-name {
  margin-top: 30px;
  font-size: 14px;
  color: rgba(0, 20, 26, 0.7);
  .copy-button {
    color: rgba(0, 20, 26, 0.45);
    padding: 0 8px;
    margin-left: 8px;
  }
  .stroage-name-title {
    color: #00141a;
  }
}
.stroage-name .copy-button:hover {
  color: #00a0cc;
}
.span-line {
  display: inline-block;
  width: 1px;
  height: 14px;
  background-color: rgba(0, 20, 26, 0.45);
  margin: 3px 20px 0 20px;
  font-size: 14px;
  vertical-align: sub;
}
/* // tab面板中的数据展示 */
.overview-data-box {
  box-sizing: border-box;
  margin-bottom: 32px;
  .resource-overview {
    display: flex;
    justify-content: space-between;
    .overview-title {
      font-weight: 600;
      color: #121f2c;
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      font-size: 14px;
    }
    .overview-title::before {
      content: '';
      width: 4px;
      height: 14px;
      background-color: @jt-primary-color;
      margin-right: 8px;
      /* // transform: translateY(1px); */
    }
  }
}
.text-weight {
  color: #00141a;
  font-size: 16px;
  font-weight: 600;
}

.card-detail {
  .card-detail-name {
    font-size: 14px;
    color: #00141a;
    line-height: 22px;
    display: flex;
    align-items: center;
    padding: 8px 0;
    &::before {
      content: '';
      width: 4px;
      height: 14px;
      background-color: @jt-primary-color;
      margin-right: 8px;
    }
  }
}
:deep(.ant-descriptions) {
  padding-top: 12px;
  background: rgba(0, 20, 26, 0.02);
  border-radius: 4px;
  font-size: 14px;
  line-height: 22px;
  margin-bottom: 4px;
  .info-item {
    padding-bottom: 12px;
    .ant-descriptions-item-container {
      white-space: nowrap;
      .ant-descriptions-item-label {
        text-align: end;
        min-width: 126px;
        display: inline-block;
        white-space: nowrap;
        color: rgba(0, 20, 26, 0.7);
      }
      .ant-descriptions-item-content {
        white-space: nowrap;
        color: #00141a;
      }
    }
  }
}
.flex {
  display: flex;
}
</style>

<style lang="less">
.card-modal .ant-modal-footer {
  margin-top: 28px;
}
</style>
